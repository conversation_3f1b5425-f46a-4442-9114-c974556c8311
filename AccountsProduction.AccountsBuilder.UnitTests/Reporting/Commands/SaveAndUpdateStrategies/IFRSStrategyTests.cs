﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.Commands.SaveAndUpdateStrategies;

public class IFRSStrategyTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Mock<ILogger<IFRSStrategy>> _loggerMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly AccountsProductionReportingDbContext _dbContext;
    private readonly IFRSStrategy _ifrsStrategy;

    public IFRSStrategyTests()
    {
        _mediatorMock = new Mock<IMediator>();
        _loggerMock = new Mock<ILogger<IFRSStrategy>>();
        _mapperMock = new Mock<IMapper>();
        var options = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>()
           .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()).Options;
        _dbContext = new AccountsProductionReportingDbContext(options);
        _dbContext.Database.EnsureCreated();
        _ifrsStrategy = new IFRSStrategy(_mediatorMock.Object, _loggerMock.Object, _dbContext, _mapperMock.Object);
    }

    [Fact]
    public void Name_should_return_expected_value()
    {
        var result = _ifrsStrategy.Name;
        Assert.Equal(ReportType.IFRS, result);
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Process_Empty_IFRS_Data_Successfully()
    {
        var data = CreateTestAccountsBuilderReportingMessageDto();
        var cancellationToken = CancellationToken.None;

        await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationToken);
        Assert.True(true);
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Handle_Null_IFRS_Data()
    {
        var data = CreateTestAccountsBuilderReportingMessageDto();
        data.IFRSIncomeStatementData = null;
        data.IFRSStatementOfFinancialPositionData = null;
        var cancellationToken = CancellationToken.None;
        await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationToken);
        Assert.True(true);
    }

    [Fact]
    public void IsMatch_Should_Return_True_For_IFRS_ReportType()
    {
        var reportType = ReportType.IFRS;

        var result = _ifrsStrategy.IsMatch(reportType);

        Assert.True(result);
    }

    [Fact]
    public void IsMatch_Should_Return_False_For_Non_IFRS_ReportType()
    {
        var reportType = ReportType.FRS102;

        var result = _ifrsStrategy.IsMatch(reportType);
        Assert.False(result);
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Handle_CancellationToken()
    {

        var data = CreateTestAccountsBuilderReportingMessageDto();
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationTokenSource.Token));
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Process_With_Empty_Collections()
    {

        var data = CreateTestAccountsBuilderReportingMessageDto();
        data.IFRSIncomeStatementData = new List<IFRSIncomeStatementMessage>();
        data.IFRSStatementOfFinancialPositionData = new List<IFRSStatementOfFinancialPositionMessage>();
        data.OtherData = new List<OtherMessage>();
        var cancellationToken = CancellationToken.None;

        await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationToken);
        Assert.True(true);
    }

    [Fact]
    public void Constructor_Should_Initialize_With_Valid_Dependencies()
    {
        var strategy = new IFRSStrategy(_mediatorMock.Object, _loggerMock.Object, _dbContext, _mapperMock.Object);

        Assert.NotNull(strategy);
        Assert.Equal(ReportType.IFRS, strategy.Name);
    }

    [Fact]
    public void Constructor_Should_Accept_Null_Mediator()
    {
        var strategy = new IFRSStrategy(null, _loggerMock.Object, _dbContext, _mapperMock.Object);
        Assert.NotNull(strategy);
    }

    [Fact]
    public void Constructor_Should_Accept_Null_Logger()
    {
        var strategy = new IFRSStrategy(_mediatorMock.Object, null, _dbContext, _mapperMock.Object);
        Assert.NotNull(strategy);
    }

    [Fact]
    public void Constructor_Should_Accept_Null_DbContext()
    {
        var strategy = new IFRSStrategy(_mediatorMock.Object, _loggerMock.Object, null, _mapperMock.Object);
        Assert.NotNull(strategy);
    }

    [Fact]
    public void Constructor_Should_Accept_Null_Mapper()
    {
        var strategy = new IFRSStrategy(_mediatorMock.Object, _loggerMock.Object, _dbContext, null);
        Assert.NotNull(strategy);
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Handle_Data_With_Invalid_Guids()
    {
        var data = CreateTestAccountsBuilderReportingMessageDto();
        data.ClientId = Guid.Empty;
        data.TenantId = Guid.Empty;
        data.PeriodId = Guid.Empty;
        var cancellationToken = CancellationToken.None;

        await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationToken);
        Assert.True(true);
    }

    [Fact]
    public void Name_Property_Should_Be_Consistent()
    {
        var name1 = _ifrsStrategy.Name;
        var name2 = _ifrsStrategy.Name;

        Assert.Equal(name1, name2);
        Assert.Equal(ReportType.IFRS, name1);
        Assert.Equal(ReportType.IFRS, name2);
    }

    [Fact]
    public void IsMatch_Should_Be_Consistent_For_Same_Input()
    {
        var reportType = ReportType.IFRS;

        var result1 = _ifrsStrategy.IsMatch(reportType);
        var result2 = _ifrsStrategy.IsMatch(reportType);

        Assert.Equal(result1, result2);
        Assert.True(result1);
        Assert.True(result2);
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Handle_Large_Empty_Collections()
    {
        var data = CreateTestAccountsBuilderReportingMessageDto();
        data.ReportingPeriods = new List<PeriodDto>(1000);
        data.IFRSIncomeStatementData = new List<IFRSIncomeStatementMessage>(1000);
        data.IFRSStatementOfFinancialPositionData = new List<IFRSStatementOfFinancialPositionMessage>(1000);
        data.OtherData = new List<OtherMessage>(1000);
        var cancellationToken = CancellationToken.None;

        await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationToken);
        Assert.True(true);
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Handle_Data_With_Different_ReportTypes()
    {
        var data = CreateTestAccountsBuilderReportingMessageDto();
        data.ReportType = ReportType.FRS102;
        var cancellationToken = CancellationToken.None;

        await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationToken);
        Assert.True(true);
    }

    [Fact]
    public void Strategy_Should_Maintain_State_Between_Calls()
    {
        var name1 = _ifrsStrategy.Name;
        var match1 = _ifrsStrategy.IsMatch(ReportType.IFRS);
        var name2 = _ifrsStrategy.Name;
        var match2 = _ifrsStrategy.IsMatch(ReportType.IFRS);

        Assert.Equal(name1, name2);
        Assert.Equal(match1, match2);
        Assert.Equal(ReportType.IFRS, name1);
        Assert.True(match1);
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Handle_Timeout_Scenarios()
    {
        var data = CreateTestAccountsBuilderReportingMessageDto();
        var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromMilliseconds(1)); // Very short timeout

        try
        {
            await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationTokenSource.Token);
            Assert.True(true);
        }
        catch (OperationCanceledException)
        {
            Assert.True(true);
        }
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Not_Modify_Input_Data()
    {
        var data = CreateTestAccountsBuilderReportingMessageDto();
        var originalClientId = data.ClientId;
        var originalTenantId = data.TenantId;
        var originalPeriodId = data.PeriodId;
        var originalReportType = data.ReportType;
        var cancellationToken = CancellationToken.None;

        await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationToken);

        Assert.Equal(originalClientId, data.ClientId);
        Assert.Equal(originalTenantId, data.TenantId);
        Assert.Equal(originalPeriodId, data.PeriodId);
        Assert.Equal(originalReportType, data.ReportType);
    }

    [Fact]
    public void Strategy_Should_Be_Thread_Safe_For_Read_Operations()
    {
        var tasks = new List<Task<bool>>();

        for (int i = 0; i < 10; i++)
        {
            tasks.Add(Task.Run(() => _ifrsStrategy.IsMatch(ReportType.IFRS)));
            tasks.Add(Task.Run(() => _ifrsStrategy.Name == ReportType.IFRS));
        }

        Task.WaitAll(tasks.ToArray());
        Assert.True(tasks.All(t => t.Result));
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Handle_Disposed_CancellationToken()
    {
        var data = CreateTestAccountsBuilderReportingMessageDto();
        var cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = cancellationTokenSource.Token;
        cancellationTokenSource.Dispose();

        await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationToken);
        Assert.True(true);
    }

    [Fact]
    public void Strategy_Should_Have_Consistent_Behavior_Across_Instances()
    {
        var strategy2 = new IFRSStrategy(_mediatorMock.Object, _loggerMock.Object, _dbContext, _mapperMock.Object);

        var name1 = _ifrsStrategy.Name;
        var name2 = strategy2.Name;
        var match1 = _ifrsStrategy.IsMatch(ReportType.IFRS);
        var match2 = strategy2.IsMatch(ReportType.IFRS);

        Assert.Equal(name1, name2);
        Assert.Equal(match1, match2);
        Assert.Equal(ReportType.IFRS, name1);
        Assert.True(match1);
    }

    [Fact]
    public async Task SendSaveAndUpdateDataEvents_Should_Handle_Data_With_Extreme_Guid_Values()
    {
        var data = CreateTestAccountsBuilderReportingMessageDto();
        data.ClientId = Guid.Parse("ffffffff-ffff-ffff-ffff-ffffffffffff");
        data.TenantId = Guid.Parse("********-0000-0000-0000-************");
        data.PeriodId = Guid.Parse("********-1234-5678-9abc-********9abc");
        var cancellationToken = CancellationToken.None;

        await _ifrsStrategy.SendSaveAndUpdateDataEvents(data, cancellationToken);
        Assert.True(true);
    }

    #region Helper Methods
    private AccountsBuilderReportingMessageDto CreateTestAccountsBuilderReportingMessageDto()
    {
        return new AccountsBuilderReportingMessageDto
        {
            ClientId = TestHelpers.Guids.GuidOne,
            TenantId = TestHelpers.Guids.GuidTwo,
            PeriodId = TestHelpers.Guids.GuidThree,
            ReportType = ReportType.IFRS,
            ReportingPeriods = new List<PeriodDto>(),
            IFRSIncomeStatementData = new List<IFRSIncomeStatementMessage>(),
            IFRSStatementOfFinancialPositionData = new List<IFRSStatementOfFinancialPositionMessage>(),
            OtherData = new List<OtherMessage>()
        };
    }
    #endregion

    [Fact]
    public void GetDplCalcs_Should_Assign_PeriodIds_And_ClientId_For_IncomeStatement()
    {
        // Arrange
        var mapperMock = new Mock<IMapper>();
        var mediatorMock = new Mock<IMediator>();
        var loggerMock = new Mock<ILogger<IFRSStrategy>>();
        var options = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;
        var dbContext = new AccountsProductionReportingDbContext(options);

        var strategy = new IFRSStrategy(mediatorMock.Object, loggerMock.Object, dbContext, mapperMock.Object);

        var period1 = new PeriodDto { Id = Guid.NewGuid(), EndDate = DateTime.UtcNow.AddDays(-1) };
        var period2 = new PeriodDto { Id = Guid.NewGuid(), EndDate = DateTime.UtcNow };
        var data = new AccountsBuilderReportingMessageDto
        {
            ClientId = Guid.NewGuid(),
            ReportingPeriods = new List<PeriodDto> { period1, period2 },
            IFRSIncomeStatementData = new List<IFRSIncomeStatementMessage>
            {
                new IFRSIncomeStatementMessage { Period = period2.EndDate }
            },
            IFRSStatementOfFinancialPositionData = new List<IFRSStatementOfFinancialPositionMessage>()
        };

        // Setup mapping to return a DplSummaryCalcs with correct PeriodId
        mapperMock.Setup(m => m.Map<List<DplSummaryCalcs>>(It.IsAny<List<IFRSIncomeStatementMessage>>()))
            .Returns<List<IFRSIncomeStatementMessage>>(src =>
                src.Select(x => new DplSummaryCalcs { ClientId = data.ClientId }).ToList());

        // Act
        var method = typeof(IFRSStrategy).GetMethod("GetDplCalcs", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var result = (List<DplSummaryCalcs>)method.Invoke(strategy, new object[] { data, "incomeStatetment" });

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(data.ClientId, x.ClientId));
    }

    [Fact]
    public void GetDplCalcs_Should_Assign_PeriodIds_And_ClientId_For_StatementOfFinancialPosition()
    {
        // Arrange
        var mapperMock = new Mock<IMapper>();
        var mediatorMock = new Mock<IMediator>();
        var loggerMock = new Mock<ILogger<IFRSStrategy>>();
        var options = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;
        var dbContext = new AccountsProductionReportingDbContext(options);

        var strategy = new IFRSStrategy(mediatorMock.Object, loggerMock.Object, dbContext, mapperMock.Object);

        var period1 = new PeriodDto { Id = Guid.NewGuid(), EndDate = DateTime.UtcNow.AddDays(-1) };
        var period2 = new PeriodDto { Id = Guid.NewGuid(), EndDate = DateTime.UtcNow };
        var data = new AccountsBuilderReportingMessageDto
        {
            ClientId = Guid.NewGuid(),
            ReportingPeriods = new List<PeriodDto> { period1, period2 },
            IFRSIncomeStatementData = new List<IFRSIncomeStatementMessage>(),
            IFRSStatementOfFinancialPositionData = new List<IFRSStatementOfFinancialPositionMessage>
            {
                new IFRSStatementOfFinancialPositionMessage { Period = period2.EndDate }
            }
        };

        // Setup mapping to return a DplSummaryCalcs with correct PeriodId
        mapperMock.Setup(m => m.Map<List<DplSummaryCalcs>>(It.IsAny<List<IFRSStatementOfFinancialPositionMessage>>()))
            .Returns<List<IFRSStatementOfFinancialPositionMessage>>(src =>
                src.Select(x => new DplSummaryCalcs { ClientId = data.ClientId }).ToList());

        // Act
        var method = typeof(IFRSStrategy).GetMethod("GetDplCalcs", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var result = (List<DplSummaryCalcs>)method.Invoke(strategy, new object[] { data, "StatementOfFinancialPosition" });

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(data.ClientId, x.ClientId));
    }

    [Fact]
    public async Task ExtractAndStoreIFRSLineItems_Should_Not_Store_When_LineItems_Empty()
    {
        // Arrange
        var mapperMock = new Mock<IMapper>();
        var mediatorMock = new Mock<IMediator>();
        var loggerMock = new Mock<ILogger<IFRSStrategy>>();
        var options = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;
        var dbContext = new AccountsProductionReportingDbContext(options);

        var strategy = new IFRSStrategy(mediatorMock.Object, loggerMock.Object, dbContext, mapperMock.Object);

        var data = new AccountsBuilderReportingMessageDto
        {
            ClientId = Guid.NewGuid(),
            ReportingPeriods = new List<PeriodDto>
            {
                new PeriodDto { Id = Guid.NewGuid(), EndDate = DateTime.UtcNow }
            },
            IFRSIncomeStatementData = new List<IFRSIncomeStatementMessage>(),
            IFRSStatementOfFinancialPositionData = new List<IFRSStatementOfFinancialPositionMessage>(),
            OtherData = new List<OtherMessage>()
        };

        // Setup MapToIFRSLineItems to return empty list
        mapperMock.Setup(m => m.Map<List<LineItem>>(It.IsAny<List<IFRSIncomeStatementMessage>>()))
            .Returns(new List<LineItem>());
        mapperMock.Setup(m => m.Map<List<LineItem>>(It.IsAny<List<IFRSStatementOfFinancialPositionMessage>>()))
            .Returns(new List<LineItem>());
        mapperMock.Setup(m => m.Map<List<LineItem>>(It.IsAny<List<OtherMessage>>()))
            .Returns(new List<LineItem>());

        var method = typeof(IFRSStrategy).GetMethod("ExtractAndStoreIFRSLineItems", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        // Act
        var task = (Task)method.Invoke(strategy, new object[] { data, CancellationToken.None });
        await task;

        // Assert
        Assert.Empty(dbContext.LineItems);
    }
}