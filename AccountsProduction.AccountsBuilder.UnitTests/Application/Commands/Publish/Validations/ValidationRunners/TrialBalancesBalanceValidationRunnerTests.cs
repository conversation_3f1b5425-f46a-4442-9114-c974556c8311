﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.MembersTransactionsRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TrialBalancesBalanceRunner;
using AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels;
using Shouldly;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners;

public class TrialBalancesBalanceValidationRunnerTests
{
    private readonly AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder _accountsBuilder;

    public TrialBalancesBalanceValidationRunnerTests()
    {
        _accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder { 
            TrialBalance = new AccountsProduction.AccountsBuilder.Domain.TrialBalance
            {
                IsBalanced = false,
                IsFundsBalanced = true
            },
        };
    }

    [Fact]
    public void Should_trigger_validation_if_trial_balances_imbalance()
    {
        var issues = new TrialBalancesBalanceValidationRunner().Validate(_accountsBuilder);

        var trialBalancesBalanceIssue = issues.FirstOrDefault();
        trialBalancesBalanceIssue.ShouldNotBeNull();
        trialBalancesBalanceIssue.ErrorCode.ShouldBe(TrialBalancesBalanceValidation.TrialBalancesBalanceConfig.ErrorCode);
        trialBalancesBalanceIssue.DisplayName.ShouldBe(TrialBalancesBalanceValidation.TrialBalancesBalanceConfig.DisplayName);
        trialBalancesBalanceIssue.ErrorCategory.ShouldBe(TrialBalancesBalanceValidation.TrialBalancesBalanceConfig.ErrorCategory);
        trialBalancesBalanceIssue.Target.ShouldBe(TrialBalancesBalanceValidation.TrialBalancesBalanceConfig.Target);
        trialBalancesBalanceIssue.Type.ShouldBe(TrialBalancesBalanceValidation.TrialBalancesBalanceConfig.Type);
        trialBalancesBalanceIssue.Name.ShouldBe(TrialBalancesBalanceValidation.TrialBalancesBalanceConfig.Name);
        trialBalancesBalanceIssue.Description.ShouldBe(TrialBalancesBalanceValidation.TrialBalancesBalanceConfig.Description);
        trialBalancesBalanceIssue.Breadcrumb.ShouldBe("Source data tab");
    }

    [Fact]
    public void Should_not_trigger_validation_if_trial_balances_balance()
    {
        _accountsBuilder.TrialBalance.IsBalanced = true;
        _accountsBuilder.TrialBalance.IsFundsBalanced = true;

        var issues = new TrialBalancesBalanceValidationRunner().Validate(_accountsBuilder);

        issues.Count.ShouldBe(0);
    }
}
