﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Application.Common;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations
{
    public class ValidationBreadcrumbBuilderTests
    {
        [Fact]
        public void Should_provide_correct_breadcrumb_for_info_tab()
        {
            var result = ClientManagementBreadcrumbs.InformationTab.ToString();
            result.ShouldBe("Client management > Information tab");
        }

        [Fact]
        public void Should_provide_correct_breadcrumb_for_rel_tab()
        {
            var result = ClientManagementBreadcrumbs.RelationShipTab.ToString();
            result.ShouldBe("Client management > Relationships tab");
        }

        [Fact]
        public void Should_provide_correct_breadcrumb_for_average_number_of_employees_frs105()
        {
            var result = NotesBreadcrumbs.AverageNumberOfEmployeesRule(ReportStandardType.FRS105).ToString();
            result.ShouldBe("Accounts Builder > Sections FRS105 > Notes to the financial statements > Average number of employees");
        }

        [Fact]
        public void Should_provide_correct_breadcrumb_for_average_number_of_employees_frs102_1a()
        {
            var result = NotesBreadcrumbs.AverageNumberOfEmployeesRule(ReportStandardType.FRS102_1A).ToString();
            result.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Notes to the financial statements > Average number of employees");
        }

        [Fact]
        public void Should_provide_correct_breadcrumb_for_average_number_of_employees_frs102()
        {
            var result = NotesBreadcrumbs.AverageNumberOfEmployeesRule(ReportStandardType.FRS102).ToString();
            result.ShouldBe("Accounts Builder > Sections FRS102 > Notes to the financial statements > Average number of employees");
        }

        [Fact]
        public void Should_provide_correct_breadcrumb_for_members_transactions()
        {
            var result = MembersTransactionsBreadcrumbs.MembersTransactionsRule.ToString();
            result.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Members transactions");
        }

        [Fact]
        public void Should_provide_correct_breadcrumb_for_advances_credit_guarantees_FRS_105()
        {
            var result = NotesFrs105Breadcrumbs.AdvancesCreditGuaranteesRule.ToString();
            result.ShouldBe("Accounts Builder > Sections FRS105 > Notes to the financial statements > Advances, credits and guarantees granted to Directors");
        }

        [Fact]
        public void Should_provide_correct_breadcrumb_for_advances_credit_guarantees_FRS_1021A()
        {
            var result = NotesFrs1021aBreadcrumbs.AdvancesCreditGuarantees.ToString();
            result.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Notes to the financial statements > Advances, credits and guarantees granted to Directors > [Select Director]");
        }


        [Fact]
        public void Should_provide_correct_breadcrumb_for_tangible_fixed_notes_FRS_1021A()
        {
            var result = NotesFrs1021aBreadcrumbs.TangibleFixedAssetsNotesSection.ToString();
            result.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Notes to the financial statements > Tangible Fixed Assets Notes");
        }

        [Fact]
        public void Should_provide_correct_breadcrumb_for_trial_balances_imbalance()
        {
            var result = TrialBalancesBalanceBreadcrumbs.TrialBalancesBalanceRule.ToString();
            result.ShouldBe("Source data tab");
        }
    }
}