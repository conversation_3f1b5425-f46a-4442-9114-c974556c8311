﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.BalanceSheetRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.DirectorsRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.IntangibleAssetsRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.LlpDesignatedMemberRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NonFinancialRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs1021aRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs105Runner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TangibleFixedAssetsRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TrialBalancesBalanceRunner;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations
{
    public class ValidationRunnerBuilderTests
    {
        protected AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder;
        public ValidationRunnerBuilderTests()
        {
            accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                ClientId = TestHelpers.Guids.GuidOne,
                TenantId = TestHelpers.Guids.GuidThree
            };
        }

        public class FRS105ValidationRunnerBuilderTests : ValidationRunnerBuilderTests
        {
            [Fact]
            public void Should_build_correct_list_of_validation_runners_for_generic_business_type()
            {
                accountsBuilder.NonFinancialData = new NonFinancialData { BusinessType = "Limited" };
                var validationRunnerBuilder = new ValidationRunnerBuilder(new Mock<IGroupAccountSubAccountIntervalRepository>().Object);

                var runners = validationRunnerBuilder.GetValidationRunners(accountsBuilder, ReportStandardType.FRS105);
                runners.Count.ShouldBe(5);
                runners[0].ShouldBeOfType<TrialBalancesBalanceValidationRunner>();
                runners[1].ShouldBeOfType<NonFinancialValidationRunner>();
                runners[2].ShouldBeOfType<DirectorsValidationRunner>();
                runners[3].ShouldBeOfType<EmptyValidationRunner>();
                runners[4].ShouldBeOfType<NotesFrs105ValidationRunner>();

            }

            [Fact]
            public void Should_build_correct_list_of_validation_runners_for_llp_business_type()
            {
                accountsBuilder.NonFinancialData = new NonFinancialData
                {
                    BusinessType = "LLP"
                };

                var validationRunnerBuilder = new ValidationRunnerBuilder(new Mock<IGroupAccountSubAccountIntervalRepository>().Object);

                var runners = validationRunnerBuilder.GetValidationRunners(accountsBuilder, ReportStandardType.FRS105);
                runners.Count.ShouldBe(5);
                runners[0].ShouldBeOfType<TrialBalancesBalanceValidationRunner>();
                runners[1].ShouldBeOfType<NonFinancialValidationRunner>();
                runners[2].ShouldBeOfType<LlpDesignatedMemberValidationRunner>();
                runners[3].ShouldBeOfType<EmptyValidationRunner>();
                runners[4].ShouldBeOfType<NotesFrs105ValidationRunner>();
            }
        }

        public class FRS1021AValidationRunnerBuilderTests : ValidationRunnerBuilderTests
        {
            [Fact]
            public void Should_build_correct_list_of_validation_runners_for_FRS1021A()
            {
                accountsBuilder.NonFinancialData = new NonFinancialData() { BusinessType = "Limited" };
                var validationRunnerBuilder = new ValidationRunnerBuilder(new Mock<IGroupAccountSubAccountIntervalRepository>().Object);

                var runners = validationRunnerBuilder.GetValidationRunners(accountsBuilder, ReportStandardType.FRS102_1A);
                runners.Count.ShouldBe(8);
                runners.SingleOrDefault(x => x.GetType() == typeof(TrialBalancesBalanceValidationRunner)).ShouldNotBeNull();
                runners.SingleOrDefault(x => x.GetType() == typeof(IntangibleAssetsValidationRunner)).ShouldNotBeNull();
                runners.SingleOrDefault(x => x.GetType() == typeof(TangibleFixedAssetsValidationRunner)).ShouldNotBeNull();
                runners.SingleOrDefault(x => x.GetType() == typeof(BalanceSheetValidationRunner)).ShouldNotBeNull();
                runners.SingleOrDefault(x => x.GetType() == typeof(NonFinancialValidationRunner)).ShouldNotBeNull();
                runners.SingleOrDefault(x => x.GetType() == typeof(DirectorsValidationRunner)).ShouldNotBeNull();
                runners.SingleOrDefault(x => x.GetType() == typeof(NotesFrs1021aValidationRunner)).ShouldNotBeNull();
            }
        }

        public class BusinessTypeValidationRunnerBuilderTests : ValidationRunnerBuilderTests
        {
            [Theory]
            [InlineData(BusinessTypes.LlpBusinessType)]
            [InlineData(BusinessTypes.LimitedBusinessType)]
            public void Should_build_correct_list_of_nonfinancialdata_validation_runners_for_llp_limited(string businessType)
            {
                accountsBuilder.NonFinancialData = new NonFinancialData { BusinessType = businessType };
                var validationRunnerBuilder = new ValidationRunnerBuilder(new Mock<IGroupAccountSubAccountIntervalRepository>().Object);

                var runners = validationRunnerBuilder.GetValidationRunners(accountsBuilder, ReportStandardType.FRS102_1A);

                runners[1].ShouldBeOfType<NonFinancialValidationRunner>();
            }

            [Theory]
            [InlineData(BusinessTypes.PartnershipBusinessType)]
            [InlineData(BusinessTypes.SoleTraderBusinessType)]
            public void Should_build_correct_list_of_nonfinancialdata_validation_runners_for_partnership_soletrader(string businessType)
            {
                accountsBuilder.NonFinancialData = new NonFinancialData { BusinessType = businessType };
                var validationRunnerBuilder = new ValidationRunnerBuilder(new Mock<IGroupAccountSubAccountIntervalRepository>().Object);

                var runners = validationRunnerBuilder.GetValidationRunners(accountsBuilder, ReportStandardType.UNINCORPORATED);

                runners[1].ShouldBeOfType<BaseNonFinancialValidationRunner>();
            }
        }
    }
}