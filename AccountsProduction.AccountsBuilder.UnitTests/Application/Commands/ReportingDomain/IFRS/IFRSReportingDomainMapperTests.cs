﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.Common.Toolkit.Utils;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Moq;
using Xunit;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using Shouldly;
using DeepEqual.Syntax;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.IFRS;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.ReportingDomain.IFRS;

public class IFRSReportingDomainMapperTests
{
    private readonly Mock<UserContext> _userContext;
    private readonly IMapper _mapper;

    public IFRSReportingDomainMapperTests()
    {
        _userContext = new Mock<UserContext>();
        _userContext.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidFour.ToString());
        _userContext.Setup(x => x.CorrelationId).Returns(TestHelpers.Guids.GuidFive.ToString());
        _userContext.Setup(x => x.UserId).Returns(TestHelpers.Guids.GuidSix.ToString());
        _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
    }

    [Theory]
    [InlineData("Active", null)]
    [InlineData("Trial", "IRIS Elements Accounts Production Trial Version")]
    public void Should_map_correct_data(string licenseStatus, string watermarkText)
    {
        var isTrialAPLicense = licenseStatus.Equals("Trial");

        _userContext.Setup(x => x.Licenses).Returns(new List<Iris.Platform.WebApi.Infrastructure.Licenses.License>
            {
                new Iris.Platform.WebApi.Infrastructure.Licenses.License() { Code = APLicense.Name, IsTrial = isTrialAPLicense}
            });

        var entitySetup = new EntitySetup { IndependentReviewType = "Accountants" };
        var reportingStandardDetail = new ReportingStandard
        {
            Id = "679767ada054710b064f02d9",
            Name = "IFRS - Full",
            Type = "IFRS",
            Version = ReportingStandardVersion.Full
        };
        var licenseData = new LicenseData(isTrialAPLicense);

        var accountsBuilder = ReportingDomainMapperTestData.GetAccountsBuilderData(BusinessTypes.LimitedBusinessType, entitySetup, reportingStandardDetail);
        accountsBuilder.UpdateLicenseData(licenseData);

        var ifrsReportingDomainMapper = new IFRSReportingDomainMapper(_userContext.Object, _mapper);

        var message = (FRS1021AAndFRS102SharedReportingMessage)ifrsReportingDomainMapper.Map(accountsBuilder);

        var expectedIFRSIncomeStatement = _mapper.Map<IFRSIncomeStatementMessage>(accountsBuilder.FinancialData.Financials.First());
        var expectedIFRSStatementOfFinancialPosition = _mapper.Map<IFRSStatementOfFinancialPositionMessage>(accountsBuilder.FinancialData.Financials.First());
        ifrsReportingDomainMapper.ReportStandardType.ShouldBe(ReportStandardType.IFRS);

        message.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);

        message.ReportType.ShouldBe(ReportStandardType.IFRS);
        message.ClientId.ShouldBe(accountsBuilder.ClientId);
        message.PeriodId.ShouldBe(accountsBuilder.PeriodId);
        message.TenantId.ShouldBe(accountsBuilder.TenantId);
        message.WatermarkText.ShouldBe(watermarkText);

        message.IFRSIncomeStatementData.First().PeriodId.ShouldBe(TestHelpers.Guids.GuidSeven);
        message.IFRSIncomeStatementData.First().WithDeepEqual(expectedIFRSIncomeStatement).IgnoreDestinationProperty(x => x.PeriodId).Assert();

        message.IFRSStatementOfFinancialPositionData.First().PeriodId.ShouldBe(TestHelpers.Guids.GuidSeven);
        message.IFRSStatementOfFinancialPositionData.First().WithDeepEqual(expectedIFRSStatementOfFinancialPosition).IgnoreDestinationProperty(x => x.PeriodId).Assert();
    }
}