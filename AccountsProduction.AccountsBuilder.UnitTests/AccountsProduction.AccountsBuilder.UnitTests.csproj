﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<IsPackable>false</IsPackable>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<ProjectGuid>{********-2222-2222-2222-********2222}</ProjectGuid>
		<AssemblyName>accountsproduction-accountsbuilder-func-unittests</AssemblyName>
		<PublishReadyToRun>true</PublishReadyToRun>
	</PropertyGroup>
	<ItemGroup>
		<Compile Remove="Application\**" />
		<Compile Remove="ArchitectureTests\**" />
		<Compile Remove="Controllers\**" />
		<Compile Remove="Filters\**" />
		<Compile Remove="Helper\**" />
		<Compile Remove="Infrastructure\**" />
		<EmbeddedResource Remove="Application\**" />
		<EmbeddedResource Remove="ArchitectureTests\**" />
		<EmbeddedResource Remove="Controllers\**" />
		<EmbeddedResource Remove="Filters\**" />
		<EmbeddedResource Remove="Helper\**" />
		<EmbeddedResource Remove="Infrastructure\**" />
		<None Remove="Application\**" />
		<None Remove="ArchitectureTests\**" />
		<None Remove="Controllers\**" />
		<None Remove="Filters\**" />
		<None Remove="Helper\**" />
		<None Remove="Infrastructure\**" />
	</ItemGroup>
	<ItemGroup>
		<Compile Include="Application\Aggregate\AggregationStrategies\AccountPeriodDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\DataProcessingLayerEventbusFinishedSuccessfulEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\DataProcessingLayerFinishedEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\EntitySetupDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\NotesAccountingPoliciesDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\DataScreenValueDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\NotesFinancialDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\PartnershipProfitShareDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\InvolvementsDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\NonFinancialDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\PracticeDetailsDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\RoundingOptionsChangedEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\SignatoriesDataEventStrategyTests.cs" />
		<Compile Include="Application\Aggregate\AggregationStrategies\TrialBalanceDataEventStrategyTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\AccountingPoliciesMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\BalanceSheetMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\AccountPeriodMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\EntitySetupMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\InvolvementMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\LicenseDataMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\NonFinancialDataDtoMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\NotesMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\PracticeDetailsMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\ProfitAndLossMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\ReportingPeriodMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\SignatoryMapperTests.cs" />
		<Compile Include="Application\Commands\GenerateReport\GenerateReportCommandTests.cs" />
		<Compile Include="Application\Commands\GenerateReport\GenerateReportCommandValidatorTests.cs" />
		<Compile Include="Application\Aggregate\Services\GenerateReportDataServiceTests.cs" />
		<Compile Include="Application\Commands\ProcessAggregationCommandTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationBreadcrumbBuilderTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationHandlerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunnerBuilderTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\BalanceSheetValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\DirectorsValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\IntangibleAssetsValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\LlpDesignatedMemberValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\MembersTransactionsValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\NonFinancialValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\NotesFrs1021aValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\NotesFrs105ValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\NotesValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\PartnershipPartnerValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\SoleTraderProprietorValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\TangibleFixedAssetsValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidationRunners\TrialBalancesBalanceValidationRunnerTests.cs" />
		<Compile Include="Application\Commands\Publish\Validations\ValidatorTests.cs" />
		<Compile Include="Application\Commands\ReportingDomain\Charity\CharityReportingDomainMapperTests.cs" />
		<Compile Include="Application\Commands\ReportingDomain\FRS102\FRS102ReportingDomainMapperTests.cs" />
		<Compile Include="Application\Commands\ReportingDomain\FRS1021A\FRS1021AReportingDomainMapperTests.cs" />
		<Compile Include="Application\Commands\ReportingDomain\FRS105\FRS105ReportingDomainMapperTests.cs" />
		<Compile Include="Application\Commands\ReportingDomain\IFRS\IFRSReportingDomainMapperTests.cs" />
		<Compile Include="Application\Commands\ReportingDomain\Unincorporated\UnincorporatedReportingDomainMapperTests.cs" />
		<Compile Include="Application\AutoMapper\Mappers\DplDataMapperTests.cs" />
		<Compile Include="Application\Common\DataScreensValueMapperTests.cs" />
		<Compile Include="Application\Common\ListSearchExtensionTests.cs" />
		<Compile Include="Application\Queries\AccountsBuilder\GetFullReportQueryTests.cs" />
		<Compile Include="Application\Queries\AccountsBuilder\GetReportQueryTests.cs" />
		<Compile Include="Controllers\AccountsBuilderControllerTests.cs" />
		<Compile Include="Controllers\HealthCheckControllerTests.cs" />
		<Compile Include="Controllers\ReportsControllerTests.cs" />
		<Compile Include="Helper\BreadcrumbSectionHelperTest.cs" />
		<Compile Include="Helper\DplMappingHelperTest.cs" />
		<Compile Include="Helper\LicenseHelperTests.cs" />
		<Compile Include="Helper\ObjectToPrimitiveConverterTests.cs" />
		<Compile Include="Infrastructure\AwsHelper\AwsHelperTests.cs" />
		<Compile Include="Infrastructure\IntegrationEvents\TrialBalanceChangeNotificationEventHandlerTests.cs" />
		<Compile Include="Infrastructure\Repositories\AccountsBuilderRepositoryTests.cs" />
		<Compile Include="Infrastructure\Services\DomainEventServiceTests.cs" />
		<Compile Include="Infrastructure\Services\TrialBalanceServiceTests.cs" />
		<Compile Include="Infrastructure\Services\AccountsBuilderServiceTests.cs" />
		<Compile Include="Infrastructure\Services\AccountPeriodServiceTests.cs" />
		<Compile Include="Infrastructure\Services\AccountsProductionServiceTests.cs" />
		<Compile Include="Infrastructure\Services\ClientDataServiceTests.cs" />
		<Compile Include="Infrastructure\Services\SnsServiceClientTests.cs" />
		<Compile Include="Infrastructure\Services\SqsServiceClientTests.cs" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Amazon.Lambda.TestUtilities" Version="2.0.0" />
		<PackageReference Include="coverlet.msbuild" Version="6.0.4">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="DeepEqual" Version="5.1.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="Moq.EntityFrameworkCore" Version="8.0.1.7" />
		<PackageReference Include="Shouldly" Version="4.3.0" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.0.1">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.AspNetCore.Razor.Language" Version="6.0.36" />
		<PackageReference Include="Iris.AccountsProduction.Common.Toolkit" Version="2.0.0.259" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.11" />
		<DotNetCliToolReference Include="dotnet-xunit" Version="2.3.1" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.AggregatorFunc.Application\AccountsProduction.AccountsBuilder.AggregatorFunc.Application.csproj" />
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.AggregatorFunction\AccountsProduction.AccountsBuilder.AggregatorFunc.csproj" />
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.Api\AccountsProduction.AccountsBuilder.Api.csproj" />
	</ItemGroup>
</Project>
