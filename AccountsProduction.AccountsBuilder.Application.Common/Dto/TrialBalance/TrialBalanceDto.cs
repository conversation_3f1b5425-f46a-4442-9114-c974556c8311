﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance
{
    public class TrialBalanceDto
    {
        public Guid PeriodId { get; set; }
        public Guid ClientId { get; set; }
        public int AccountsChartId { get; set; }
        public string AccountsChartIdentifier { get; set; } = null!;
        public int GroupStructureId { get; set; }
        public int GroupStructureCode { get; set; }
        public string Error { get; set; } = null!;
        public bool? IsSuccessful { get; set; }
        public DateTime EntityModificationTime { get; set; }
        public List<PeriodTrialBalanceDto> TrialBalances { get; set; } = [];
        public List<ReportingPeriodDto> ReportingPeriods { get; set; } = [];
        public List<SectorInfoDto> SectorsInformation { get; set; } = [];
        public bool IsBalanced { get; set; }
        public bool IsFundsBalanced { get; set; }
    }
}
