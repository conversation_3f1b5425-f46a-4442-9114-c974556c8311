﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Domain;
using Amazon.S3.Model;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Newtonsoft.Json.Linq;
using System;
using System.Diagnostics.Contracts;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData
{
    public class FinancialDto
    {
        public Guid PeriodId { get; set; }
        public DateTime Period { get; set; }

        public FinancialDataCategoryMessage Turnover { get; set; } = null!;

        public FinancialDataCategoryMessage OtherIncome { get; set; } = null!;

        public FinancialDataCategoryMessage CostOfRawMaterialsAndConsumables { get; set; } = null!;

        public FinancialDataCategoryMessage StaffCosts { get; set; } = null!;

        public FinancialDataCategoryMessage DepreciationAndOtherAmountsWrittenOffAssets { get; set; } = null!;

        public FinancialDataCategoryMessage OtherCharges { get; set; } = null!;

        public FinancialDataCategoryMessage Tax { get; set; } = null!;

        public FinancialDataCategoryMessage CrossCheck { get; set; } = null!;

        public FinancialDataCategoryMessage CalledUpShareCapitalNotPaid { get; set; } = null!;

        public FinancialDataCategoryMessage FixedAssets { get; set; } = null!;

        public FinancialDataCategoryMessage CurrentAssets { get; set; } = null!;

        public FinancialDataCategoryMessage PrepaymentsAndAccruedIncome { get; set; } = null!;

        public FinancialDataCategoryMessage CreditorsAmountsFallingDueWithinOneYear { get; set; } = null!;

        public FinancialDataCategoryMessage NetCurrentAssetsOrLiabilities { get; set; } = null!;

        public FinancialDataCategoryMessage TotalAssetsLessCurrentLiabilities { get; set; } = null!;

        public FinancialDataCategoryMessage CreditorsAmountsFallingAfterMoreThanOneYear { get; set; } = null!;

        public FinancialDataCategoryMessage ProvisionsForLiabilities { get; set; } = null!;

        public FinancialDataCategoryMessage AccrualsAndDeferredIncome { get; set; } = null!;

        public FinancialDataCategoryMessage NetAssets { get; set; } = null!;

        public FinancialDataCategoryMessage CapitalAndReserves { get; set; } = null!;

        public FinancialDataCategoryMessage Sales { get; set; } = null!;

        public FinancialDataCategoryMessage CostOfSales { get; set; } = null!;

        public FinancialDataCategoryMessage Expenses { get; set; } = null!;

        public FinancialDataCategoryMessage FinanceCosts { get; set; } = null!;

        public FinancialDataCategoryMessage PartnerAppropriations { get; set; } = null!;

        public FinancialDataCategoryMessage Depreciation { get; set; } = null!;

        public FinancialDataCategoryMessage IntangibleAssets { get; set; } = null!;

        public FinancialDataCategoryMessage TangibleFixedAssets { get; set; } = null!;

        public FinancialDataCategoryMessage FixedAssetInvestments { get; set; } = null!;

        public FinancialDataCategoryMessage InvestmentProperty { get; set; } = null!;

        public FinancialDataCategoryMessage Stock { get; set; } = null!;

        public FinancialDataCategoryMessage Debtors { get; set; } = null!;

        public FinancialDataCategoryMessage CashAtBankAndInHand { get; set; } = null!;

        public FinancialDataCategoryMessage CapitalAccount { get; set; } = null!;

        public FinancialDataCategoryMessage PartnersCapitalAccounts { get; set; } = null!;

        public FinancialDataCategoryMessage PartnersCurrentAccounts { get; set; } = null!;

        public FinancialDataCategoryMessage OtherReserves { get; set; } = null!;

        public FinancialDataCategoryMessage GrossProfitLoss { get; set; } = null!;

        public FinancialDataCategoryMessage DistributionExpenses { get; set; } = null!;

        public FinancialDataCategoryMessage AdministrativeExpenses { get; set; } = null!;
        public FinancialDataCategoryMessage OtherOperatingExpenses { get; set; } = null!;


        public FinancialDataCategoryMessage OtherOperatingIncome { get; set; } = null!;

        public FinancialDataCategoryMessage GainLossG50OnRevaluation { get; set; } = null!;

        public FinancialDataCategoryMessage OperatingProfitLoss { get; set; } = null!;

        public FinancialDataCategoryMessage ExceptionalItems { get; set; } = null!;

        public FinancialDataCategoryMessage IncomeFromSharesInGroupUndertakings { get; set; } = null!;

        public FinancialDataCategoryMessage IncomeFromInterestInAssociatedUndertakings { get; set; } = null!;

        public FinancialDataCategoryMessage IncomeFromOtherParticipatingInterests { get; set; } = null!;

        public FinancialDataCategoryMessage IncomeFromFixedAssetInvestments { get; set; } = null!;

        public FinancialDataCategoryMessage InterestReceivableAndSimilarIncome { get; set; } = null!;

        public FinancialDataCategoryMessage OtherFinanceIncome { get; set; } = null!;

        public FinancialDataCategoryMessage AmountsWrittenOffInvestments { get; set; } = null!;

        public FinancialDataCategoryMessage GainLossG282OnRevaluation { get; set; } = null!;

        public FinancialDataCategoryMessage InterestPayableAndSimilarExpenses { get; set; } = null!;

        public FinancialDataCategoryMessage OtherFinanceCosts { get; set; } = null!;

        public FinancialDataCategoryMessage ProfitLossOnOrdinaryActivitiesBeforeTaxation { get; set; } = null!;

        public FinancialDataCategoryMessage Taxation { get; set; } = null!;

        public FinancialDataCategoryMessage ProfitLossForTheFinancialYear { get; set; } = null!;

        public FinancialDataCategoryMessage PensionSchemeAssetsLiabilities { get; set; } = null!;

        public FinancialDataCategoryMessage HealthcareObligations { get; set; } = null!;

        public FinancialDataCategoryMessage CalledUpShareCapital { get; set; } = null!;

        public FinancialDataCategoryMessage SharePremiumReserve { get; set; } = null!;

        public FinancialDataCategoryMessage RevaluationReserve { get; set; } = null!;

        public FinancialDataCategoryMessage CapitalRedemptionReserve { get; set; } = null!;

        public FinancialDataCategoryMessage OtherReserves1 { get; set; } = null!;

        public FinancialDataCategoryMessage OtherReserves2 { get; set; } = null!;

        public FinancialDataCategoryMessage FairValueReserve { get; set; } = null!;

        public FinancialDataCategoryMessage ProfitLossReserve { get; set; } = null!;

        public FinancialDataCategoryMessage Goodwill { get; set; } = null!;

        public FinancialDataCategoryMessage CurrentAssetInvestments { get; set; } = null!;

        public FinancialDataCategoryMessage ProfitLossAvailableForDiscretionaryDivision { get; set; } = null!;

        public FinancialDataCategoryMessage NonControllingInterestsPL { get; set; } = null!;

        public FinancialDataCategoryMessage NonControllingInterestsBS { get; set; } = null!;

        public FinancialDataCategoryMessage MembersRemunerationAsExpense { get; set; } = null!;

        public FinancialDataCategoryMessage TotalMembersInterests { get; set; } = null!;

        public FinancialDataCategoryMessage MembersOtherInterests { get; set; } = null!;

        public FinancialDataCategoryMessage MembersCapital { get; set; } = null!;

        public FinancialDataCategoryMessage OtherDebtsDueToMembers { get; set; } = null!;

        public FinancialDataCategoryMessage LoansAndOtherDebtsDueToMembers { get; set; } = null!;

        public FinancialDataCategoryMessage HerdBasis { get; set; } = null!;
        public FinancialDataCategoryMessage WagesAndSalaries { get; set; } = null!;
        public FinancialDataCategoryMessage SocialSecurityCosts { get; set; } = null!;
        public FinancialDataCategoryMessage OtherPensionCosts { get; set; } = null!;
        public FinancialDataCategoryMessage DirectorsRemuneration { get; set; } = null!;
        public FinancialDataCategoryMessage DirectorsContributionsToDbSchemes { get; set; } = null!;
        public FinancialDataCategoryMessage DirectorsContributionsToDcSchemes { get; set; } = null!;
        public FinancialDataCategoryMessage DirectorsPensionsPaid { get; set; } = null!;
        public FinancialDataCategoryMessage DirectorsCompensationForLossOfOffice { get; set; } = null!;
        public FinancialDataCategoryMessage ShareCapitalMovements { get; set; } = null!;
        public FinancialDataCategoryMessage Revenue { get; set; } = null!;
        public FinancialDataCategoryMessage GrossProfit { get; set; } = null!;

        public FinancialDataCategoryMessage GainLossOnRevaluationOfAssets { get; set; } = null!;
        public FinancialDataCategoryMessage DistributionCosts { get; set; } = null!;

        public FinancialDataCategoryMessage OperatingProfitBeforeExceptionalItems { get; set; } = null!;

        public FinancialDataCategoryMessage OperatingProfit { get; set; } = null!;
        public FinancialDataCategoryMessage FinanceIncome { get; set; } = null!;
        public FinancialDataCategoryMessage ProfitBeforeIncomeTax { get; set; } = null!;
        public FinancialDataCategoryMessage IncomeTax { get; set; } = null!;
        public FinancialDataCategoryMessage ProfitForTheFinancialYear { get; set; } = null!;
        public FinancialDataCategoryMessage IntangibleAssetsOwned { get; set; } = null!;
        public FinancialDataCategoryMessage PropertyPlantAndEquipmentOwned { get; set; } = null!;
        public FinancialDataCategoryMessage InvestmentPropertyOwned { get; set; } = null!;
        public FinancialDataCategoryMessage IntangibleAssetsROU { get; set; } = null!;
        public FinancialDataCategory PropertyPlantAndEquipmentROU { get; set; } = null!;
        public FinancialDataCategory InvestmentPropertyROU { get; set; } = null!;
        public FinancialDataCategoryMessage InvestmentInAssociates { get; set; } = null!;
        public FinancialDataCategoryMessage NCAInvestments { get; set; } = null!;
        public FinancialDataCategoryMessage LoansAndOtherFinancialAssets { get; set; } = null!;
        public FinancialDataCategoryMessage PensionAsset { get; set; } = null!;
        public FinancialDataCategoryMessage RetirementHealthcareBenefitsAsset { get; set; } = null!;
        public FinancialDataCategoryMessage NCATradeAndOtherReceivables { get; set; } = null!;

        public FinancialDataCategoryMessage NCAContractAssets { get; set; } = null!;
        public FinancialDataCategoryMessage NCATaxReceivable { get; set; } = null!;

        public FinancialDataCategoryMessage NCADeferredTax { get; set; } = null!;

        public FinancialDataCategoryMessage Inventories { get; set; } = null!;

        public FinancialDataCategoryMessage CATradeAndOtherReceivables { get; set; } = null!;
        public FinancialDataCategoryMessage CAContractAssets { get; set; } = null!;

        public FinancialDataCategoryMessage CATaxReceivable { get; set; } = null!;
        public FinancialDataCategoryMessage CAInvestments { get; set; } = null!;

        public FinancialDataCategoryMessage CashAndCashEquivalents { get; set; } = null!;
        public FinancialDataCategoryMessage Prepayments { get; set; } = null!;
        public FinancialDataCategoryMessage CLTradeAndOtherPayables { get; set; } = null!;
        public FinancialDataCategoryMessage CLContractLiabilities { get; set; } = null!;
        public FinancialDataCategoryMessage CLFinancialLiabilitiesBorrowings { get; set; } = null!;

        public FinancialDataCategoryMessage CLTaxPayable { get; set; } = null!;
        public FinancialDataCategoryMessage CLProvisions { get; set; } = null!;
        public FinancialDataCategoryMessage NCLTradeAndOtherPayables { get; set; } = null!;

        public FinancialDataCategoryMessage NCLContractLiabilities { get; set; } = null!;
        public FinancialDataCategoryMessage NCLFinancialLiabilitiesBorrowings { get; set; } = null!;

        public FinancialDataCategoryMessage NCLTaxPayable { get; set; } = null!;

        public FinancialDataCategoryMessage NCLDeferredTax { get; set; } = null!;
        public FinancialDataCategoryMessage PensionLiabilities { get; set; } = null!;
        public FinancialDataCategoryMessage RetirementHealthcareBenefitsLiability { get; set; } = null!;

        public FinancialDataCategoryMessage SharePremium { get; set; } = null!;

        public FinancialDataCategoryMessage RetainedEarnings { get; set; } = null!;

    }
}
