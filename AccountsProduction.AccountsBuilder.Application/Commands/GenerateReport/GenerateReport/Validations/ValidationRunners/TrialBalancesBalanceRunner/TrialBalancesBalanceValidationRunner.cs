﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.PartnershipPartnersRunner;
using AccountsProduction.AccountsBuilder.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TrialBalancesBalanceRunner;

public class TrialBalancesBalanceValidationRunner : ValidationRunner
{
    protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
        ValidationRules =>  new()
        {
            {
                TrialBalancesBalanceValidation.TrialBalancesBalance, accountsBuilder =>
                {
                    var hasBalancedTrialBalances = accountsBuilder.TrialBalance.IsBalanced && accountsBuilder.TrialBalance.IsFundsBalanced;

                    if (!hasBalancedTrialBalances)
                    {
                        return TrialBalancesBalanceValidation.TrialBalancesBalanceConfig.MapToValidationIssue();
                    }

                    return null;
                }
            }
        };
}

