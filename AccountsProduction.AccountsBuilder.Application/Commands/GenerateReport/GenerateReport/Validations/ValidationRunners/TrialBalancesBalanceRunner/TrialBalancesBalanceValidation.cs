﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TrialBalancesBalanceRunner;
public static class TrialBalancesBalanceValidation
{
    public const string TrialBalancesBalance = "TrialBalancesBalance";

    public static readonly ValidationRuleConfig TrialBalancesBalanceConfig =
        new ValidationRuleConfig
        {
            Breadcrumb = TrialBalancesBalanceBreadcrumbs.TrialBalancesBalanceRule.ToString(),
            Description = "The trial balance is not balanced. Please make adjustments on the Source Data or Reports & Adjustments tab.",
            Name = TrialBalancesBalance,
            Type = ValidationRuleType.Invalid,
            Target = Target.IssueLog,
            ErrorCategory = ErrorCategoryType.Mandatory,
            DisplayName = "Imbalanced Trial Balance",
            ErrorCode = ValidationCodes.TrialBalancesBalance
        };
}