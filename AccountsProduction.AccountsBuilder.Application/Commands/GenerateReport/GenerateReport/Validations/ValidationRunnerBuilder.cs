﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.BalanceSheetRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.DirectorsRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.IntangibleAssetsRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.LlpDesignatedMemberRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.MembersTransactionsRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NonFinancialRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs1021aRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs105Runner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.PartnershipPartnersRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.SoleTraderProprietorRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TangibleFixedAssetsRunner;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TrialBalancesBalanceRunner;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations
{
    public class ValidationRunnerBuilder : IValidationRunnerBuilder
    {
        private readonly IGroupAccountSubAccountIntervalRepository _repository;

        private readonly List<ValidationRunner> _runners;

        private readonly Dictionary<string, Func<ValidationRunner>> _businessTypeRelatedValidationRunners =
            new()
            {
                { BusinessTypes.LlpBusinessType, () => new LlpDesignatedMemberValidationRunner() },
                { BusinessTypes.LimitedBusinessType, () => new DirectorsValidationRunner() },
                { BusinessTypes.PartnershipBusinessType, () => new PartnershipPartnerValidationRunner() },
                { BusinessTypes.SoleTraderBusinessType, () => new SoleTraderProprietorValidationRunner() }
            };

        public ValidationRunnerBuilder(IGroupAccountSubAccountIntervalRepository repository)
        {
            _repository = repository;
            _runners = new List<ValidationRunner>();
        }

        public IList<ValidationRunner> GetValidationRunners(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, string reportType)
        {
            _runners.Add(new TrialBalancesBalanceValidationRunner());
            _runners.AddRange(GetSharedValidationRunners(accountsBuilder));
            _runners.Add(GetMembersTransactionsValidation(accountsBuilder, reportType));

            switch (reportType)
            {
                case ReportStandardType.FRS105:
                    _runners.Add(new NotesFrs105ValidationRunner(_repository));
                    return _runners;
                case ReportStandardType.FRS102_1A:
                    _runners.Add(new IntangibleAssetsValidationRunner(_repository));
                    _runners.Add(new TangibleFixedAssetsValidationRunner(_repository));
                    _runners.Add(new BalanceSheetValidationRunner());
                    _runners.Add(new NotesFrs1021aValidationRunner(_repository));
                    return _runners;
                default:
                    return _runners;
            }
        }

        private List<ValidationRunner> GetSharedValidationRunners(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (string.IsNullOrEmpty(accountsBuilder.NonFinancialData.BusinessType))
            {
                return new List<ValidationRunner>
                {
                    new EmptyValidationRunner()
                };
            }

            return new List<ValidationRunner>
            {
                GetNonFinancialValidationRunnerByBusinessType(accountsBuilder.NonFinancialData.BusinessType),
                GetInvolvementTypeValidatorRunner(accountsBuilder.NonFinancialData.BusinessType),
            };
        }

        private ValidationRunner GetInvolvementTypeValidatorRunner(string businessType)
        {
            _businessTypeRelatedValidationRunners.TryGetValue(businessType, out var businessTypeRelatedValidator);
            return businessTypeRelatedValidator != null ? businessTypeRelatedValidator() : new EmptyValidationRunner();
        }

        private ValidationRunner GetNonFinancialValidationRunnerByBusinessType(string businessType)
        {
            return businessType.Equals(BusinessTypes.PartnershipBusinessType) ||
                businessType.Equals(BusinessTypes.SoleTraderBusinessType) ?
                new BaseNonFinancialValidationRunner() : new NonFinancialValidationRunner();
        }

        private ValidationRunner GetMembersTransactionsValidation(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, string reportType)
        {
            return accountsBuilder.NonFinancialData.BusinessType.Equals(BusinessTypes.LlpBusinessType) && reportType.Equals(ReportStandardType.FRS102_1A) ? new MembersTransactionsValidationRunner() : new EmptyValidationRunner();
        }
    }
}