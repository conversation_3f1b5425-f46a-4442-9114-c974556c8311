﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AccountsProduction.AccountsBuilder.Reporting.Application;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Application.Reporting.Commands.SaveAndUpdateStrategies;

public class IFRSStrategy : FRS102BaseStrategy
{
    private readonly ILogger<IFRSStrategy> _logger;
    private readonly IAccountsProductionReportingDbContext _accountsProductionReportingDbContext;
    private readonly IMapper _mapper;

    public IFRSStrategy(
        IMediator mediator,
        ILogger<IFRSStrategy> logger,
        IAccountsProductionReportingDbContext accountsProductionReportingDbContext,
        IMapper mapper) : base(mediator, logger, accountsProductionReportingDbContext, mapper)
    {
        _logger = logger;
        _accountsProductionReportingDbContext = accountsProductionReportingDbContext;
        _mapper = mapper;
    }

    public override string ReportTypeName => ReportType.IFRS;

    public override async Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        await base.SendSaveAndUpdateDataEvents(data, cancellationToken);

        await SaveAndUpdateOtherDplCalc(data, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateOthers for {reportType}", DateTime.UtcNow, ReportType.IFRS);

        await SaveAndUpdateIFRSLineItems(data, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateLineItems", DateTime.UtcNow);

        SaveAndUpdateIFRSIncomeStatement(data, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateProfitAndLoss for {reportType}", DateTime.UtcNow, ReportType.IFRS);

        SaveAndUpdateIFRSStatementOfFinancialPosition(data, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateBalanceSheet for {reportType}", DateTime.UtcNow, ReportType.IFRS);

        await SaveAndUpdateNotes(data, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateNotes for {reportType}", DateTime.UtcNow, ReportType.IFRS);
    }

    protected virtual async Task SaveAndUpdateIFRSLineItems(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Saving line items");

        if (!data.IFRSIncomeStatementData.HasLineItems() && !data.IFRSStatementOfFinancialPositionData.HasLineItems() && !data.OtherData.HasLineItems()) return;

        if (data.ReportingPeriods == null) { throw new InvalidOperationException(); }

        RemoveExistingLineItemsForCurrentPeriod();

        await ExtractAndStoreIFRSLineItems(data, cancellationToken);
    }

    private void RemoveExistingLineItemsForCurrentPeriod()
    {
        var dbLineItems = ReportingPeriod?.LineItem;

        _logger.LogInformation("Removing {Count} Line Items.", dbLineItems?.Count);

        if (dbLineItems != null && dbLineItems.Any())
            _accountsProductionReportingDbContext.LineItems.RemoveRange(dbLineItems);
    }

    private async Task ExtractAndStoreIFRSLineItems(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        var lineItems = GetIFRSLineItemsForCurrentPeriodFrom(data);
        if (!lineItems.Any())
        {
            _logger.LogInformation("LineItems are empty!");
            return;
        }
        _logger.LogInformation("Storing {Count} Line Items.", lineItems.Count);

        await _accountsProductionReportingDbContext.LineItems.AddRangeAsync(lineItems, cancellationToken);
    }

    protected virtual List<LineItem> MapToIFRSLineItems(AccountsBuilderReportingMessageDto data)
    {
        var IFRSIncomeStatementItems = _mapper.Map<List<LineItem>>(data.IFRSIncomeStatementData);
        var IFRSStatementOfFinancialPositionItems = _mapper.Map<List<LineItem>>(data.IFRSStatementOfFinancialPositionData);
        var otherLineItems = _mapper.Map<List<LineItem>>(data.OtherData);

        var lineItems = IFRSIncomeStatementItems.Concat(IFRSStatementOfFinancialPositionItems).Concat(otherLineItems).ToList();

        _logger.LogInformation("Mapped {Count} Line Items", lineItems.Count);

        return lineItems;
    }

    private List<LineItem> GetIFRSLineItemsForCurrentPeriodFrom(AccountsBuilderReportingMessageDto data)
    {
        var currentPeriodId = data.ReportingPeriods.MaxBy(x => x.EndDate)!.Id;

        var lineItems = new List<LineItem>();
        List<LineItem> mappedLineItems;

        mappedLineItems = MapToIFRSLineItems(data);

        lineItems.AddRange(mappedLineItems);

        lineItems.ForEach(x => { x.ClientId = data.ClientId; });

        _logger.LogInformation("LineItems - count: {lineItemsCount}", lineItems.Count);

        lineItems = LineItemHelper.MergeItems(lineItems, data.ReportingPeriods);

        _logger.LogInformation("LineItems after merge");

        lineItems = lineItems.Where(x => x.AccountPeriodId == currentPeriodId).ToList();

        return lineItems;
    }

    protected async virtual void SaveAndUpdateIFRSIncomeStatement(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Saving IFRS Income Statement data.");

        if (!data.IFRSIncomeStatementData.Any()) return;

        if (data.ReportingPeriods == null) { throw new InvalidOperationException(); }


        await ExtractAndStoreDplCalc(data, cancellationToken, "incomeStatetment");
    }


    private async Task ExtractAndStoreDplCalc(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken, string messageType)
    {
        var dplSummaryCalcs = GetDplCalcs(data, messageType);
        if (!dplSummaryCalcs.Any())
        {
            _logger.LogInformation("DPLSummaryCalcs are empty!");
            return;
        }

        _logger.LogInformation("Storing {Count} DLPSummaryCalcs.", dplSummaryCalcs.Count);
        await _accountsProductionReportingDbContext.DplSummaryCalcs.AddRangeAsync(dplSummaryCalcs, cancellationToken);
    }

    private List<DplSummaryCalcs> GetDplCalcs(AccountsBuilderReportingMessageDto data, string messageType)
    {
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);
        var previousPeriod = data.ReportingPeriods.MinBy(x => x.EndDate);
        var dplSummaryCalcs = new List<DplSummaryCalcs>();

        if (messageType == "incomeStatetment")
        {
            foreach (var item in data.IFRSIncomeStatementData)
            {
                if (item.Period.Date == currentPeriod?.EndDate.Date)
                {
                    item.PeriodId = currentPeriod.Id;
                }
                else
                {
                    item.PeriodId = previousPeriod.Id;
                }
            }
            dplSummaryCalcs.AddRange(MapIFRSIncomeStatementToDplCalc(data));

        }
        else
        {
            foreach (var item in data.IFRSStatementOfFinancialPositionData)
            {
                if (item.Period.Date == currentPeriod?.EndDate.Date)
                {
                    item.PeriodId = currentPeriod.Id;
                }
                else
                {
                    item.PeriodId = previousPeriod.Id;
                }
            }
            dplSummaryCalcs.AddRange(MapIFRSStatementOfFinancialPositionToDplCalc(data));

        }


        dplSummaryCalcs.ForEach(x => { x.ClientId = data.ClientId; });
        _logger.LogInformation("dplSummaryCalcs items count: {Count}", dplSummaryCalcs.Count);
        return dplSummaryCalcs;
    }

    protected List<DplSummaryCalcs> MapIFRSIncomeStatementToDplCalc(AccountsBuilderReportingMessageDto data)
    {
        if (data.IFRSIncomeStatementData == null)
        {
            return null;
        }
        var uniqueIncomeStatementData = data.IFRSIncomeStatementData.DistinctBy(o => o.PeriodId).ToList();
        var dplSummaryCalcs = _mapper.Map<List<DplSummaryCalcs>>(uniqueIncomeStatementData);

        _logger.LogInformation("Mapped {Count} Line Items", dplSummaryCalcs.Count);
        return dplSummaryCalcs;
    }

    protected List<DplSummaryCalcs> MapIFRSStatementOfFinancialPositionToDplCalc(AccountsBuilderReportingMessageDto data)
    {
        if (data.IFRSStatementOfFinancialPositionData == null)
        {
            return null;
        }
        var uniqueStatementOfFinancialPositionData = data.IFRSStatementOfFinancialPositionData.DistinctBy(o => o.PeriodId).ToList();
        var dplSummaryCalcs = _mapper.Map<List<DplSummaryCalcs>>(uniqueStatementOfFinancialPositionData);

        _logger.LogInformation("Mapped {Count} Line Items", dplSummaryCalcs.Count);
        return dplSummaryCalcs;
    }

    protected async virtual void SaveAndUpdateIFRSStatementOfFinancialPosition(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Saving IFRS Statement of Financial Position data.");

        if (!data.IFRSStatementOfFinancialPositionData.Any()) return;

        if (data.ReportingPeriods == null) { throw new InvalidOperationException(); }


        await ExtractAndStoreDplCalc(data, cancellationToken, "StatementOfFinancialPosition");
    }
}