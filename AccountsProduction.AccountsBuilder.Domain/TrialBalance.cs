﻿using Amazon.DynamoDBv2.DataModel;
using Iris.Elements.DynamoDb.Converters;

namespace AccountsProduction.AccountsBuilder.Domain
{
    public class TrialBalance
    {
        public Guid PeriodId { get; set; }
        public Guid ClientId { get; set; }
        public int AccountsChartId { get; set; }
        public string AccountsChartIdentifier { get; set; }
        public int GroupStructureId { get; set; }
        public int GroupStructureCode { get; set; }
        public bool? IsSuccessful { get; set; }
        public string Error { get; set; }
        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime EntityModificationTime { get; set; }
        public List<PeriodTrialBalance> TrialBalances { get; set; } = [];
        public List<ReportingPeriod> ReportingPeriods { get; set; } = [];
        public List<SectorInfo> SectorsInformation { get; set; } = [];
        public bool IsBalanced { get; set; }
        public bool IsFundsBalanced { get; set; }

        public void UpdateModificationTime()
        {
            EntityModificationTime = DateTime.UtcNow;
        }

    }
}
